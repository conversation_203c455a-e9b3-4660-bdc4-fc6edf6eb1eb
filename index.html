
<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Earth & Solar System Dynamics</title>
  <script src="https://cdn.tailwindcss.com"></script>
  <style>
    body { margin: 0; font-family: sans-serif; background-color: white; color: black; }
    #root { width: 100vw; height: 100vh; display: flex; flex-direction: column; }
    canvas { display: block; } /* Ensure canvas takes up block space */
  </style>
<script type="importmap">
{
  "imports": {
    "react/": "https://esm.sh/react@^19.1.0/",
    "react": "https://esm.sh/react@^19.1.0",
    "react-dom/": "https://esm.sh/react-dom@^19.1.0/",
    "three": "https://esm.sh/three@0.165.0"
  }
}
</script>
<link rel="stylesheet" href="/index.css">
</head>
<body>
  <noscript>You need to enable JavaScript to run this app.</noscript>
  <div id="root"></div>
  <script type="module" src="/index.tsx"></script>
</body>
</html>