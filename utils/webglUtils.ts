
export function loadShader(gl: WebGLRenderingContext, type: number, source: string): WebGLShader | null {
  const shader = gl.createShader(type);
  if (!shader) {
    console.error("Unable to create shader.");
    return null;
  }

  gl.shaderSource(shader, source);
  gl.compileShader(shader);

  if (!gl.getShaderParameter(shader, gl.COMPILE_STATUS)) {
    console.error(
      `An error occurred compiling the shaders: ${gl.getShaderInfoLog(shader)}`
    );
    gl.deleteShader(shader);
    return null;
  }

  return shader;
}

export function initShaderProgram(gl: WebGLRenderingContext, vsSource: string, fsSource: string): WebGLProgram | null {
  const vertexShader = loadShader(gl, gl.VERTEX_SHADER, vsSource);
  const fragmentShader = loadShader(gl, gl.FRAGMENT_SHADER, fsSource);

  if (!vertexShader || !fragmentShader) {
    return null;
  }

  const shaderProgram = gl.createProgram();
  if (!shaderProgram) {
    console.error("Unable to create shader program.");
    return null;
  }
  gl.attachShader(shaderProgram, vertexShader);
  gl.attachShader(shaderProgram, fragmentShader);
  gl.linkProgram(shaderProgram);

  if (!gl.getProgramParameter(shaderProgram, gl.LINK_STATUS)) {
    console.error(
      `Unable to initialize the shader program: ${gl.getProgramInfoLog(
        shaderProgram
      )}`
    );
    return null;
  }

  return shaderProgram;
}

// Creates a simple 2D projection matrix
export function createProjectionMatrix(width: number, height: number): number[] {
  // Note: This matrix flips the Y axis so 0,0 is top left.
  // For center origin, adjustments are needed or use a different matrix.
  // This simple version assumes origin is center for world coordinates [-1, 1] mapping
  return [
    2 / width, 0, 0,
    0, -2 / height, 0, // Flip Y
    -1, 1, 1           // Translate to clip space
  ];
}

// Creates a 2D model-view matrix for translation and scaling
export function createModelViewMatrix(translateX: number, translateY: number, scaleX: number, scaleY: number): number[] {
  return [
    scaleX, 0, 0,
    0, scaleY, 0,
    translateX, translateY, 1
  ];
}

// Creates a 3x3 matrix for transformations specifically tailored for the vertex shader
// The vertex shader expects a mat3 that transforms a vec2 position (x,y)
// (u_matrix * vec3(a_position, 1)).xy
// This means the matrix should be column-major order for gl.uniformMatrix3fv, or be transposed if row-major.
// WebGL expects column-major. Our arrays are typically row-major when written linearly.
// Let's define it such that it directly maps to:
// x' = m[0]*x + m[3]*y + m[6]
// y' = m[1]*x + m[4]*y + m[7]
// This is if we interpret the array as column-major for uniformMatrix3fv.
// If we write it as: [a, b, c, d, e, f, g, h, i]
// and interpret as column-major:
// a d g
// b e h
// c f i
//
// Standard 2D transformation matrix (row-major for multiplication M * v):
// [ scaleX   0      translateX ]
// [ 0      scaleY   translateY ]
// [ 0        0      1          ]
//
// For gl.uniformMatrix3fv, it expects column-major order.
// So, if we have matrix M, we need to pass M^T (transpose) if `transpose` argument is false.
// Or, pass M directly if `transpose` argument is true.
// Let's stick to creating the matrix in column-major order directly.
// x' = m0*x + m3*y + m6
// y' = m1*x + m4*y + m7
// z' = m2*x + m5*y + m8
//
// For 2D affine transformation:
// x_out = scale_x * cos(rot) * x_in - scale_y * sin(rot) * y_in + translate_x
// y_out = scale_x * sin(rot) * x_in + scale_y * cos(rot) * y_in + translate_y
// For no rotation:
// x_out = scale_x * x_in + translate_x
// y_out = scale_y * y_in + translate_y
//
// Matrix (column-major):
// [ scale_x,     0,         0 ]
// [ 0,           scale_y,   0 ]
// [ translate_x, translate_y, 1 ]
// This becomes the array [scale_x, 0, 0, 0, scale_y, 0, translate_x, translate_y, 1]

export function createTransformMatrix(translateX: number, translateY: number, scaleX: number, scaleY: number): Float32Array {
  return new Float32Array([
    scaleX, 0, 0,
    0, scaleY, 0,
    translateX, translateY, 1
  ]);
}


export function createCircleVertices(radius: number, segments: number): Float32Array {
  const vertices = [];
  for (let i = 0; i <= segments; i++) {
    const angle = (i / segments) * 2 * Math.PI;
    vertices.push(radius * Math.cos(angle));
    vertices.push(radius * Math.sin(angle));
  }
  return new Float32Array(vertices);
}