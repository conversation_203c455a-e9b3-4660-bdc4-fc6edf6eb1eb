
export const OBLIQ_DEG = 23.44; // Earth's axial tilt in degrees
export const OBLIQ_RAD = OBLIQ_DEG * Math.PI / 180;

export const AXIS_MARGIN = 35; // Reduced margin for axes and labels

// Solar Declination Range
export const X_DECL_MIN = -OBLIQ_DEG;
export const X_DECL_MAX = OBLIQ_DEG;

// Insolation Percentage Range on Y-axis
export const Y_INSOL_MIN = 0;
export const Y_INSOL_MAX = 100;

export const INSOL_TRACK_MIN_PERCENT = 37;
export const INSOL_TRACK_MAX_PERCENT = 63;


// Ordered by Solar Longitude, starting with 冬至 (Dongzhi, Ls=270°)
export const TERM_NAMES: string[] = [
  '冬至', '小寒', '大寒', '立春', '雨水', '惊蛰',
  '春分', '清明', '谷雨', '立夏', '小满', '芒种',
  '夏至', '小暑', '大暑', '立秋', '处暑', '白露',
  '秋分', '寒露', '霜降', '立冬', '小雪', '大雪'
];

export const TERM_TO_MONTH: { [key: string]: number } = {
  '冬至': 12, '小寒': 1, '大寒': 1,
  '立春': 2, '雨水': 2, '惊蛰': 3,
  '春分': 3, '清明': 4, '谷雨': 4,
  '立夏': 5, '小满': 5, '芒种': 6,
  '夏至': 6, '小暑': 7, '大暑': 7,
  '立秋': 8, '处暑': 8, '白露': 9,
  '秋分': 9, '寒露': 10, '霜降': 10,
  '立冬': 11, '小雪': 11, '大雪': 12
};

// --- Ported from testearth2.html ---
export const TRACK_GEOMETRY_PARAMS = {
  r: 0,
  lenStraight: 0,
  s1: 0, s2: 0, s3: 0, s4: 0, per: 0,
};

export const TRACK_TRANSFORM_PARAMS = {
  p1: { x: 0, y: 0 },
  p2: { x: 0, y: 0 },
  CX: 0, CY: 0, ALPHA: 0,
};

// chartWidth is the full logical width for X-axis mapping.
// chartHeight is the height of the specific area where this chart's Y-axis and track will be drawn (e.g., logicalHeight - globalTopOffset).
// margin is applied *within* this chartHeight for Y-mapping.
export function setupTrackGeometry(
  chartWidth: number,
  chartHeight: number, // This is now chartAreaHeight
  margin: number,
  trackVisualHeight: number
) {
  TRACK_GEOMETRY_PARAMS.r = trackVisualHeight / 2;
  // lenStraight uses chartWidth and margin for X-axis space.
  TRACK_GEOMETRY_PARAMS.lenStraight = Math.max(0, chartWidth - 2 * margin - 30);
  TRACK_GEOMETRY_PARAMS.s1 = TRACK_GEOMETRY_PARAMS.lenStraight;
  TRACK_GEOMETRY_PARAMS.s2 = Math.PI * TRACK_GEOMETRY_PARAMS.r;
  TRACK_GEOMETRY_PARAMS.s3 = TRACK_GEOMETRY_PARAMS.lenStraight;
  TRACK_GEOMETRY_PARAMS.s4 = Math.PI * TRACK_GEOMETRY_PARAMS.r;
  TRACK_GEOMETRY_PARAMS.per = TRACK_GEOMETRY_PARAMS.s1 + TRACK_GEOMETRY_PARAMS.s2 + TRACK_GEOMETRY_PARAMS.s3 + TRACK_GEOMETRY_PARAMS.s4;

  // p1.x and p2.x are calculated based on chartWidth and margin.
  TRACK_TRANSFORM_PARAMS.p1 = {
    x: mapSolarDeclinationToCanvasX(X_DECL_MIN, chartWidth, margin, X_DECL_MIN, X_DECL_MAX),
    // p1.y uses chartHeight (which is chartAreaHeight) and margin for Y-mapping within that area.
    // The returned Y is relative to the top of chartHeight, including the margin.
    y: mapInsolationToCanvasY(INSOL_TRACK_MIN_PERCENT, chartHeight, margin, Y_INSOL_MIN, Y_INSOL_MAX)
  };
  TRACK_TRANSFORM_PARAMS.p2 = {
    x: mapSolarDeclinationToCanvasX(X_DECL_MAX, chartWidth, margin, X_DECL_MIN, X_DECL_MAX),
    y: mapInsolationToCanvasY(INSOL_TRACK_MAX_PERCENT, chartHeight, margin, Y_INSOL_MIN, Y_INSOL_MAX)
  };

  // CX, CY are midpoints of p1, p2. These are now relative to the chartArea's top-left.
  TRACK_TRANSFORM_PARAMS.CX = (TRACK_TRANSFORM_PARAMS.p1.x + TRACK_TRANSFORM_PARAMS.p2.x) / 2;
  TRACK_TRANSFORM_PARAMS.CY = (TRACK_TRANSFORM_PARAMS.p1.y + TRACK_TRANSFORM_PARAMS.p2.y) / 2;
  TRACK_TRANSFORM_PARAMS.ALPHA = Math.atan2(
    TRACK_TRANSFORM_PARAMS.p2.y - TRACK_TRANSFORM_PARAMS.p1.y,
    TRACK_TRANSFORM_PARAMS.p2.x - TRACK_TRANSFORM_PARAMS.p1.x
  );
}


export function getTrackLocalPositionTestEarth2(ratioAlongPerimeter: number, params: typeof TRACK_GEOMETRY_PARAMS): { x: number; y: number } {
  if (params.per === 0) return { x:0, y:0 };
  let d = (ratioAlongPerimeter * params.per) % params.per;
  if (d < 0) d += params.per;

  const xs = -params.lenStraight / 2;
  const xe = params.lenStraight / 2;
  const r0 = params.r;

  if (d <= params.s1) return { x: xs + d, y: -r0 };
  d -= params.s1;
  if (d <= params.s2) {
    const dd = d;
    const a = -Math.PI / 2 + dd / r0;
    return { x: xe + r0 * Math.cos(a), y: r0 * Math.sin(a) };
  }
  d -= params.s2;
  if (d <= params.s3) {
    const dd = d;
    return { x: xe - dd, y: r0 };
  }
  d -= params.s3;

  const dd = d;
  const a = Math.PI / 2 + dd / r0;
  return { x: xs + r0 * Math.cos(a), y: r0 * Math.sin(a) };
}

export function transformTrackLocalToCanvasTestEarth2(px: number, py: number, transformParams: typeof TRACK_TRANSFORM_PARAMS): { x: number; y: number } {
  const c = Math.cos(transformParams.ALPHA);
  const s = Math.sin(transformParams.ALPHA);
  // CX, CY are already relative to the current drawing context (e.g., after global top offset).
  // This function transforms local track coordinates (px, py) to that context.
  return {
    x: transformParams.CX + px * c - py * s,
    y: transformParams.CY + px * s + py * c
  };
}

export function mapSolarLongitudeToTrackRatioTestEarth2(LsDeg: number): number {
  return ((LsDeg - 270 + 360) % 360) / 360;
}


export function getSolarLongitudeDeg(orbitalAngleRad: number): number {
  let LsDeg = (orbitalAngleRad * 180 / Math.PI) % 360;
  if (LsDeg < 0) LsDeg += 360;
  return LsDeg;
}

export function getSolarDeclinationDeg(LsDeg: number): number {
  const LsRad = LsDeg * Math.PI / 180;
  return Math.asin(Math.sin(OBLIQ_RAD) * Math.sin(LsRad)) * 180 / Math.PI;
}

export function getInsolationPercentage(solarDeclinationDeg: number): number {
  const deltaRad = solarDeclinationDeg * Math.PI / 180;
  return 50 + 50 * Math.sin(deltaRad);
}


export function calculateDaylightHours(latitudeDeg: number, solarDeclinationDeg: number): number {
  const latRad = latitudeDeg * Math.PI / 180;
  const declRad = solarDeclinationDeg * Math.PI / 180;

  const cosH0 = -Math.tan(latRad) * Math.tan(declRad);

  if (cosH0 >= 1) return 24;
  if (cosH0 <= -1) return 0;

  const H0 = Math.acos(cosH0);
  return (2 * H0 * 12 / Math.PI);
}

export function mapSolarDeclinationToCanvasX(
  declDeg: number,
  canvasWidth: number, // Full width of the area for X-mapping
  margin: number,
  minDecl: number,
  maxDecl: number
): number {
  const chartDrawableWidth = canvasWidth - 2 * margin;
  if (maxDecl === minDecl) return margin + chartDrawableWidth / 2;
  return margin + ((declDeg - minDecl) / (maxDecl - minDecl)) * chartDrawableWidth;
}

export function mapInsolationToCanvasY(
  insolPercent: number,
  canvasHeight: number, // Height of the area for Y-mapping (e.g., chartAreaHeight)
  margin: number,
  minInsol: number,
  maxInsol: number
): number {
  const chartDrawableHeight = canvasHeight - 2 * margin;
  if (maxInsol === minInsol) return margin + chartDrawableHeight / 2;
  // Y is inverted: 0% insolation is at bottom of chart (margin + chartDrawableHeight)
  // 100% insolation is at top of chart (margin)
  // The returned Y is relative to the top of `canvasHeight`.
  return margin + (1 - (insolPercent - minInsol) / (maxInsol - minInsol)) * chartDrawableHeight;
}

export function getSolarTermIndexFromLs(LsDeg: number): number {
  return Math.round(((LsDeg - 270 + 360) % 360) / 15) % 24;
}
