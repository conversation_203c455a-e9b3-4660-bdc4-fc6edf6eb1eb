
import React, { useState } from 'react';
import SolarSystem from './components/SolarSystem';
import RotatingEarth from './components/RotatingEarth';
import InsolationChart from './components/InsolationChart';

const App: React.FC = () => {
  const [currentOrbitalAngleRad, setCurrentOrbitalAngleRad] = useState(0);

  return (
    <div className="w-screen h-screen flex flex-col items-center justify-start bg-white text-black p-3 overflow-hidden">
      <h1 className="text-3xl font-bold my-3 text-black">地球与太阳系动态模型</h1>
      <div className="flex flex-row w-full flex-grow items-stretch justify-around gap-3">
        {/* Left Section: Rotating Earth - Approx 10/30 = 33.3% of panel space */}
        <div className="flex-1 max-w-[calc((100%-1.5rem)/3)] flex flex-col items-center justify-start p-2 border border-gray-300 rounded-lg shadow-md bg-white">
          <h2 className="text-xl font-semibold mb-2 text-black">自转地球 (Three.js)</h2>
          <div className="w-full flex-grow relative rounded overflow-hidden bg-gray-800">
            <RotatingEarth />
          </div>
        </div>

        {/* Center Section: Insolation Chart - Approx 13/30 = 43.3% of panel space */}
        <div className="flex-1 max-w-[calc(1.3*(100%-1.5rem)/3)] flex flex-col items-center justify-start p-2 border border-gray-300 rounded-lg shadow-md bg-white">
          <h2 className="text-xl font-semibold mb-2 text-black">北半球光照与节气图</h2>
          <div className="w-full flex-grow relative rounded overflow-hidden flex items-center justify-center">
            <InsolationChart currentOrbitalAngleRad={currentOrbitalAngleRad} />
          </div>
        </div>

        {/* Right Section: Solar System Orbit - Approx 7/30 = 23.3% of panel space */}
        <div className="flex-1 max-w-[calc(0.7*(100%-1.5rem)/3)] flex flex-col items-center justify-start p-2 border border-gray-300 rounded-lg shadow-md bg-white">
           <h2 className="text-xl font-semibold mb-2 text-black">地球公转 (WebGL + SVG)</h2>
          <div className="flex items-center justify-center w-full h-full">
            <SolarSystem onAngleUpdate={setCurrentOrbitalAngleRad} />
          </div>
        </div>
      </div>
    </div>
  );
};

export default App;
