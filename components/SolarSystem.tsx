
import React, { useRef, useEffect, useState, useCallback } from 'react';
import { Season, WebGLProgramInfo } from '../types';
import { initShaderProgram, createTransformMatrix, createCircleVertices } from '../utils/webglUtils';

const VS_SOURCE = `
  attribute vec2 aVertexPosition;
  uniform mat3 uTransformMatrix; 

  void main() {
    gl_Position = vec4((uTransformMatrix * vec3(aVertexPosition, 1.0)).xy, 0.0, 1.0);
  }
`;

const FS_SOURCE = `
  precision mediump float;
  uniform vec4 uColor;

  void main() {
    gl_FragColor = uColor;
  }
`;

const ORBIT_RX_BASE = 150; 
const ORBIT_RY_BASE = 80;  
const SUN_RADIUS_WORLD_BASE = 15;
const EARTH_RADIUS_WORLD_BASE = 7;
const DESIGN_CANVAS_WIDTH_BASE = 400; 
const DESIGN_CANVAS_HEIGHT_BASE = 280;
const ORBIT_PERIOD_MS = 20000; 

interface SolarSystemProps {
  onAngleUpdate: (angleRad: number) => void;
}

const SolarSystem: React.FC<SolarSystemProps> = ({ onAngleUpdate }) => {
  const webglCanvasRef = useRef<HTMLCanvasElement>(null);
  const svgRef = useRef<SVGSVGElement>(null);
  const containerRef = useRef<HTMLDivElement>(null);
  
  const glRef = useRef<WebGLRenderingContext | null>(null);
  const programInfoRef = useRef<WebGLProgramInfo | null>(null);
  const sunBufferRef = useRef<WebGLBuffer | null>(null);
  const earthBufferRef = useRef<WebGLBuffer | null>(null);
  const circleVerticesRef = useRef<Float32Array | null>(null);
  
  const animationFrameIdRef = useRef<number | null>(null);
  const lastTimestampRef = useRef<number>(0);
  const totalElapsedTimeRef = useRef<number>(0); 

  const [currentSeason, setCurrentSeason] = useState<Season>(Season.Spring);
  const [dimensions, setDimensions] = useState({ width: DESIGN_CANVAS_WIDTH_BASE, height: DESIGN_CANVAS_HEIGHT_BASE });

  // Calculate scaled dimensions based on current container size
  const scaleFactor = Math.min(dimensions.width / DESIGN_CANVAS_WIDTH_BASE, dimensions.height / DESIGN_CANVAS_HEIGHT_BASE);
  const ORBIT_RX = ORBIT_RX_BASE * scaleFactor;
  const ORBIT_RY = ORBIT_RY_BASE * scaleFactor;
  const SUN_RADIUS_WORLD = SUN_RADIUS_WORLD_BASE * scaleFactor;
  const EARTH_RADIUS_WORLD = EARTH_RADIUS_WORLD_BASE * scaleFactor;
  const CANVAS_WIDTH = dimensions.width;
  const CANVAS_HEIGHT = dimensions.height;


  useEffect(() => {
    const currentContainer = containerRef.current;
    if (!currentContainer) return;

    const resizeObserver = new ResizeObserver(entries => {
      for (let entry of entries) {
        const { width, height } = entry.contentRect;
         if (width > 0 && height > 0) {
            setDimensions({ width, height });
        }
      }
    });
    resizeObserver.observe(currentContainer);
    
    const { width, height } = currentContainer.getBoundingClientRect();
    if (width > 0 && height > 0) {
        setDimensions({ width, height });
    }

    return () => resizeObserver.disconnect();
  }, []);


  const getSeason = (angle: number): Season => {
    const twoPi = 2 * Math.PI;
    let normalizedAngle = angle % twoPi;
    if (normalizedAngle < 0) {
      normalizedAngle += twoPi;
    }
    
    if (normalizedAngle >= 0 && normalizedAngle < Math.PI / 2) {
      return Season.Spring; 
    } else if (normalizedAngle >= Math.PI / 2 && normalizedAngle < Math.PI) {
      return Season.Summer; 
    } else if (normalizedAngle >= Math.PI && normalizedAngle < (3 * Math.PI) / 2) {
      return Season.Autumn; 
    } else {
      return Season.Winter; 
    }
  };

  const drawScene = useCallback(() => {
    const gl = glRef.current;
    const programInfo = programInfoRef.current;
    if (!gl || !programInfo || CANVAS_WIDTH === 0 || CANVAS_HEIGHT === 0) return;

    gl.viewport(0, 0, gl.canvas.width, gl.canvas.height); // use gl.canvas.width/height for viewport
    gl.clearColor(1.0, 1.0, 1.0, 1.0); 
    gl.clear(gl.COLOR_BUFFER_BIT);
    
    // World coords are [-canvas_w/2, canvas_w/2] and [-canvas_h/2, canvas_h/2]
    // Clip coords are [-1, 1]
    // So, scaling factor is 2.0 / CANVAS_WIDTH and 2.0 / CANVAS_HEIGHT
    const worldToClipScaleX = 2.0 / CANVAS_WIDTH;
    const worldToClipScaleY = 2.0 / CANVAS_HEIGHT;

    // --- Draw Sun ---
    if (sunBufferRef.current && circleVerticesRef.current) {
      gl.useProgram(programInfo.program);
      
      // Sun is at (0,0) in world space.
      // Scale is SUN_RADIUS_WORLD. To map to clip space:
      const sunScaleClipX = SUN_RADIUS_WORLD * worldToClipScaleX;
      const sunScaleClipY = SUN_RADIUS_WORLD * worldToClipScaleY;
      const sunTransformMatrix = createTransformMatrix(0, 0, sunScaleClipX, sunScaleClipY); 
      
      gl.uniformMatrix3fv(programInfo.uniformLocations.modelViewMatrix, false, sunTransformMatrix);
      gl.uniform4f(programInfo.uniformLocations.color, 0.0, 0.0, 0.0, 1.0); // Black

      gl.bindBuffer(gl.ARRAY_BUFFER, sunBufferRef.current);
      gl.vertexAttribPointer(programInfo.attribLocations.vertexPosition, 2, gl.FLOAT, false, 0, 0);
      gl.enableVertexAttribArray(programInfo.attribLocations.vertexPosition);
      gl.drawArrays(gl.TRIANGLE_FAN, 0, circleVerticesRef.current.length / 2);
    }

    // --- Draw Earth ---
    if (earthBufferRef.current && circleVerticesRef.current) {
      gl.useProgram(programInfo.program);

      const angle = (totalElapsedTimeRef.current / ORBIT_PERIOD_MS) * 2 * Math.PI;
      onAngleUpdate(angle); 

      const earthWorldX = ORBIT_RX * Math.cos(angle);
      const earthWorldY = ORBIT_RY * Math.sin(angle);

      const earthTranslateClipX = earthWorldX * worldToClipScaleX;
      const earthTranslateClipY = earthWorldY * worldToClipScaleY;
      const earthScaleClipX = EARTH_RADIUS_WORLD * worldToClipScaleX;
      const earthScaleClipY = EARTH_RADIUS_WORLD * worldToClipScaleY;

      const earthTransformMatrix = createTransformMatrix(earthTranslateClipX, earthTranslateClipY, earthScaleClipX, earthScaleClipY);
      
      gl.uniformMatrix3fv(programInfo.uniformLocations.modelViewMatrix, false, earthTransformMatrix);
      gl.uniform4f(programInfo.uniformLocations.color, 0.0, 0.0, 0.0, 1.0); // Black

      gl.bindBuffer(gl.ARRAY_BUFFER, earthBufferRef.current);
      gl.vertexAttribPointer(programInfo.attribLocations.vertexPosition, 2, gl.FLOAT, false, 0, 0);
      gl.enableVertexAttribArray(programInfo.attribLocations.vertexPosition);
      gl.drawArrays(gl.TRIANGLE_FAN, 0, circleVerticesRef.current.length / 2);
      
      setCurrentSeason(getSeason(angle));
    }

  }, [CANVAS_WIDTH, CANVAS_HEIGHT, EARTH_RADIUS_WORLD, ORBIT_RX, ORBIT_RY, SUN_RADIUS_WORLD, onAngleUpdate]); 

  const animate = useCallback((timestamp: number) => {
    if (lastTimestampRef.current === 0) { 
      lastTimestampRef.current = timestamp;
    }
    const deltaTime = timestamp - lastTimestampRef.current;
    lastTimestampRef.current = timestamp;

    totalElapsedTimeRef.current += deltaTime;
    
    drawScene();
    animationFrameIdRef.current = requestAnimationFrame(animate);
  }, [drawScene]);


  useEffect(() => {
    const canvas = webglCanvasRef.current;
    if (!canvas || CANVAS_WIDTH === 0 || CANVAS_HEIGHT === 0) return;

    // Set canvas drawing surface size based on dynamic dimensions and DPR
    const dpr = window.devicePixelRatio || 1;
    canvas.width = CANVAS_WIDTH * dpr;
    canvas.height = CANVAS_HEIGHT * dpr;
    
    // CSS ensures visual scaling
    canvas.style.width = `${CANVAS_WIDTH}px`;
    canvas.style.height = `${CANVAS_HEIGHT}px`;


    const gl = canvas.getContext('webgl', { antialias: true });
    if (!gl) {
      console.error("Unable to initialize WebGL for SolarSystem.");
      return;
    }
    glRef.current = gl;
    // gl.viewport is called in drawScene with gl.canvas.width/height
    // Shader positions are already in clip space. DPR handled by canvas width/height attributes.
    // The vertex positions are [-1,1] for unit circle, then scaled by transform matrix.
    // The transform matrix correctly maps world to clip space using CANVAS_WIDTH/HEIGHT (logical).

    const shaderProgram = initShaderProgram(gl, VS_SOURCE, FS_SOURCE);
    if (!shaderProgram) return;

    programInfoRef.current = {
      program: shaderProgram,
      attribLocations: {
        vertexPosition: gl.getAttribLocation(shaderProgram, 'aVertexPosition'),
      },
      uniformLocations: {
        modelViewMatrix: gl.getUniformLocation(shaderProgram, 'uTransformMatrix'), 
        color: gl.getUniformLocation(shaderProgram, 'uColor'),
        projectionMatrix: null, 
      },
    };
    
    const segments = 32;
    const unitCircleVerts = createCircleVertices(1.0, segments); // Vertices are in [-1, 1] range
    circleVerticesRef.current = unitCircleVerts;

    sunBufferRef.current = gl.createBuffer();
    gl.bindBuffer(gl.ARRAY_BUFFER, sunBufferRef.current);
    gl.bufferData(gl.ARRAY_BUFFER, unitCircleVerts, gl.STATIC_DRAW);

    earthBufferRef.current = gl.createBuffer();
    gl.bindBuffer(gl.ARRAY_BUFFER, earthBufferRef.current);
    gl.bufferData(gl.ARRAY_BUFFER, unitCircleVerts, gl.STATIC_DRAW);
    
    lastTimestampRef.current = 0;
    if (animationFrameIdRef.current) cancelAnimationFrame(animationFrameIdRef.current);
    animationFrameIdRef.current = requestAnimationFrame(animate);

    const handleVisibilityChange = () => {
      if (document.hidden) {
        if (animationFrameIdRef.current) {
          cancelAnimationFrame(animationFrameIdRef.current);
          animationFrameIdRef.current = null; 
        }
      } else {
        if (!animationFrameIdRef.current) { 
          lastTimestampRef.current = 0; 
          animationFrameIdRef.current = requestAnimationFrame(animate);
        }
      }
    };

    document.addEventListener('visibilitychange', handleVisibilityChange);

    return () => {
      if (animationFrameIdRef.current) {
        cancelAnimationFrame(animationFrameIdRef.current);
      }
      document.removeEventListener('visibilitychange', handleVisibilityChange);
      const currentGl = glRef.current;
      if (currentGl) {
          if (sunBufferRef.current) currentGl.deleteBuffer(sunBufferRef.current);
          if (earthBufferRef.current) currentGl.deleteBuffer(earthBufferRef.current);
          const currentProgramInfo = programInfoRef.current;
          if (currentProgramInfo?.program) currentGl.deleteProgram(currentProgramInfo.program);
      }
    };
  }, [animate, CANVAS_WIDTH, CANVAS_HEIGHT]); // Re-init if canvas base logical size changes

  return (
    <div ref={containerRef} className="w-full h-full flex flex-col items-center justify-center" role="figure" aria-labelledby="solarsystem-caption">
      <div 
        id="solarsystem-caption"
        className="text-black text-2xl font-semibold mb-1" // Adjusted font size for potentially smaller area
        style={{ height: '30px', textAlign: 'center', flexShrink: 0 }} 
        aria-live="polite"
      >
        {currentSeason}
      </div>
      <div style={{ width: CANVAS_WIDTH, height: CANVAS_HEIGHT, position: 'relative', flexGrow: 1 }}>
        <canvas
          ref={webglCanvasRef}
          // width/height attributes set in useEffect based on CANVAS_WIDTH/HEIGHT and DPR
          // CSS style set in useEffect
          className="absolute top-0 left-0 z-0 rounded" 
          aria-label="WebGL animation of Earth orbiting the Sun"
        />
        <svg
          ref={svgRef}
          width={CANVAS_WIDTH}
          height={CANVAS_HEIGHT}
          className="absolute top-0 left-0 z-10 rounded" 
          viewBox={`0 0 ${CANVAS_WIDTH} ${CANVAS_HEIGHT}`} // SVG coordinates match logical canvas size
          aria-hidden="true"
        >
          <ellipse
            cx={CANVAS_WIDTH / 2} // Center of the SVG viewbox
            cy={CANVAS_HEIGHT / 2}
            rx={ORBIT_RX} // Scaled orbit radius
            ry={ORBIT_RY} // Scaled orbit radius
            fill="none"
            stroke="black"
            strokeWidth="1.5"
          />
        </svg>
      </div>
    </div>
  );
};

export default SolarSystem;
