
import React, { useRef, useEffect, useCallback } from 'react';
import * as THREE from 'three';

const EARTH_DAY_MAP_URL = 'https://cdn.jsdelivr.net/gh/mrdoob/three.js/examples/textures/planets/earth_atmos_2048.jpg';
const EARTH_NORMAL_MAP_URL = 'https://cdn.jsdelivr.net/gh/mrdoob/three.js/examples/textures/planets/earth_normal_2048.jpg';
const EARTH_SPECULAR_MAP_URL = 'https://cdn.jsdelivr.net/gh/mrdoob/three.js/examples/textures/planets/earth_specular_2048.jpg';

const RotatingEarth: React.FC = () => {
  const mountRef = useRef<HTMLDivElement>(null);
  const rendererRef = useRef<THREE.WebGLRenderer | null>(null);
  const animationFrameIdRef = useRef<number | null>(null);

  const sceneRef = useRef(new THREE.Scene());
  const cameraRef = useRef<THREE.PerspectiveCamera | null>(null);
  const earthMeshRef = useRef<THREE.Mesh | null>(null);

  const setupScene = useCallback((container: HTMLDivElement) => {
    const width = container.clientWidth;
    const height = container.clientHeight;

    console.log('RotatingEarth: setupScene - container dimensions:', width, 'x', height);
    if (width === 0 || height === 0) {
        console.warn('RotatingEarth: Container dimensions are zero in setupScene. Earth will not be visible.');
        // Optionally, you could set up a retry or wait for dimensions, but for now, just log.
    }

    // Renderer
    const renderer = new THREE.WebGLRenderer({ antialias: true, alpha: true });
    renderer.setSize(width, height);
    renderer.setPixelRatio(Math.min(window.devicePixelRatio, 2));
    container.appendChild(renderer.domElement);
    rendererRef.current = renderer;

    // Camera
    const camera = new THREE.PerspectiveCamera(45, width > 0 && height > 0 ? width / height : 1, 0.1, 1000);
    camera.position.set(0, 0, 3.5);
    cameraRef.current = camera;

    // Lighting
    const ambientLight = new THREE.AmbientLight(0xffffff, 0.6);
    sceneRef.current.add(ambientLight);

    const directionalLight = new THREE.DirectionalLight(0xffffff, 1.2);
    directionalLight.position.set(5, 3, 5);
    sceneRef.current.add(directionalLight);

    // Earth
    const geometry = new THREE.SphereGeometry(1, 64, 64);
    
    const fallbackMaterial = new THREE.MeshStandardMaterial({
      color: 0x4488cc, 
      roughness: 0.8,
      metalness: 0.1,
    });
    const earthMesh = new THREE.Mesh(geometry, fallbackMaterial);
    earthMesh.rotation.y = Math.PI;
    sceneRef.current.add(earthMesh);
    earthMeshRef.current = earthMesh;

    const textureLoader = new THREE.TextureLoader();

    textureLoader.load(
      EARTH_DAY_MAP_URL,
      (dayTexture) => {
        console.log('RotatingEarth: Earth day texture loaded successfully.');
        dayTexture.colorSpace = THREE.SRGBColorSpace;
        
        const texturedMaterial = new THREE.MeshStandardMaterial({
          map: dayTexture,
          metalness: 0.1,
          roughness: 0.8,
        });
        earthMesh.material = texturedMaterial;
        texturedMaterial.needsUpdate = true;

        textureLoader.load(
            EARTH_NORMAL_MAP_URL,
            (normalTexture) => {
                console.log('RotatingEarth: Earth normal map loaded successfully.');
                if (earthMesh.material instanceof THREE.MeshStandardMaterial) {
                    earthMesh.material.normalMap = normalTexture;
                    earthMesh.material.needsUpdate = true;
                }
            },
            undefined,
            (error) => {
                console.error('RotatingEarth: Earth normal map loading failed:', error);
            }
        );

        textureLoader.load(
            EARTH_SPECULAR_MAP_URL,
            (specularTexture) => {
                console.log('RotatingEarth: Earth specular map loaded successfully.');
                 if (earthMesh.material instanceof THREE.MeshStandardMaterial) {
                    earthMesh.material.specularIntensityMap = specularTexture;
                    earthMesh.material.needsUpdate = true;
                }
            },
            undefined,
            (error) => {
                console.error('RotatingEarth: Earth specular map loading failed:', error);
            }
        );

      },
      undefined,
      (error) => {
        console.error('RotatingEarth: Earth day texture loading failed, using fallback material:', error);
      }
    );

  }, []);

  const animate = useCallback(() => {
    animationFrameIdRef.current = requestAnimationFrame(animate);
    if (earthMeshRef.current) {
      earthMeshRef.current.rotation.y += 0.002;
    }
    if (rendererRef.current && cameraRef.current && sceneRef.current) { // Added sceneRef.current check
      rendererRef.current.render(sceneRef.current, cameraRef.current);
    }
  }, []);

  const handleResize = useCallback(() => {
    if (mountRef.current && rendererRef.current && cameraRef.current) {
      const container = mountRef.current;
      const width = container.clientWidth;
      const height = container.clientHeight;
      console.log('RotatingEarth: handleResize - new container dimensions:', width, 'x', height);

      if (width > 0 && height > 0) {
        rendererRef.current.setSize(width, height);
        cameraRef.current.aspect = width / height;
        cameraRef.current.updateProjectionMatrix();
      } else {
        console.warn('RotatingEarth: Container dimensions are zero in handleResize. Renderer/camera not updated.');
      }
    }
  }, []);

  useEffect(() => {
    const currentMountRef = mountRef.current;
    if (currentMountRef && !rendererRef.current) {
        // Ensure initial setup has valid dimensions
        if (currentMountRef.clientWidth > 0 && currentMountRef.clientHeight > 0) {
            setupScene(currentMountRef);
            animate();
        } else {
            // If dimensions are not yet available, observe for resize.
            // This is a basic way; a more robust solution might use ResizeObserver before initial setup.
            console.warn('RotatingEarth: Initial container dimensions are zero. Deferring setup.');
            const tempResizeObserver = new ResizeObserver(entries => {
                const entry = entries[0];
                if (entry.contentRect.width > 0 && entry.contentRect.height > 0) {
                    if (mountRef.current && !rendererRef.current) { // Check again before setup
                        console.log('RotatingEarth: Dimensions became available via ResizeObserver. Setting up scene.');
                        setupScene(mountRef.current);
                        animate();
                    }
                    tempResizeObserver.unobserve(entry.target); // Stop observing once set up
                }
            });
            tempResizeObserver.observe(currentMountRef);
            // Don't add window resize listener until setup is done.
            // It will be added by the main resize effect.
            // Return cleanup for this observer if component unmounts before setup.
            return () => {
                tempResizeObserver.disconnect();
            }
        }
    }
    
    window.addEventListener('resize', handleResize);

    return () => {
      if (animationFrameIdRef.current) {
        cancelAnimationFrame(animationFrameIdRef.current);
        animationFrameIdRef.current = null;
      }
      window.removeEventListener('resize', handleResize);
      
      const r = rendererRef.current; // temp var for current renderer
      if (r) {
        r.dispose();
        if (currentMountRef && r.domElement.parentNode === currentMountRef) {
             currentMountRef.removeChild(r.domElement);
        }
      }
      
      sceneRef.current.traverse(object => {
        if (object instanceof THREE.Mesh) {
          object.geometry?.dispose();
          const material = object.material as any; 
          if (Array.isArray(material)) {
            material.forEach(mat => {
              Object.keys(mat).forEach(key => {
                if (mat[key] instanceof THREE.Texture) {
                  mat[key].dispose();
                }
              });
              mat.dispose();
            });
          } else if (material && typeof material.dispose === 'function') {
            Object.keys(material).forEach(key => {
              if (material[key] instanceof THREE.Texture) {
                material[key].dispose();
              }
            });
            material.dispose();
          }
        }
      });
      
      while(sceneRef.current.children.length > 0){
          const child = sceneRef.current.children[0];
          sceneRef.current.remove(child);
      }
      rendererRef.current = null; // Clear ref after disposal
      cameraRef.current = null;
      earthMeshRef.current = null;
    };
  // Add handleResize to dependency array, setupScene and animate are stable due to useCallback.
  }, [setupScene, animate, handleResize]); 

  // Added a border for debugging visibility
  return <div ref={mountRef} className="w-full h-full bg-gray-800" />;
};

export default RotatingEarth;
