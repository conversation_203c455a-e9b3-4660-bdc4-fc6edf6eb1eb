
import React, { useRef, useEffect, useMemo, useState } from 'react';
import {
  TERM_NAMES,
  TERM_TO_MONTH,
  OBLIQ_DEG,
  AXIS_MARGIN,
  X_DECL_MIN, X_DECL_MAX, Y_INSOL_MIN, Y_INSOL_MAX,
  // mapSolarDeclinationToCanvasX, // Not directly used here, but in utils
  // mapInsolationToCanvasY, // Not directly used here, but in utils
  getSolarLongitudeDeg,
  calculateDaylightHours,
  getSolarTermIndexFromLs,
  setupTrackGeometry,
  TRACK_GEOMETRY_PARAMS,
  TRACK_TRANSFORM_PARAMS,
  getTrackLocalPositionTestEarth2,
  transformTrackLocalToCanvasTestEarth2,
  mapSolarLongitudeToTrackRatioTestEarth2,
} from '../utils/insolationChartUtils';

interface InsolationChartProps {
  currentOrbitalAngleRad: number;
}

const TRACK_VISUAL_HEIGHT = 15;

const STAGGER_BASE_OFFSET = 30;
const STAGGER_LEVEL_HEIGHT = 15;
const NUM_STAGGER_LEVELS = 3;
const GLOBAL_TOP_OFFSET = 35; // Space for daylight hours text + some padding

const InsolationChart: React.FC<InsolationChartProps> = ({ currentOrbitalAngleRad }) => {
  const canvasRef = useRef<HTMLCanvasElement>(null);
  const containerRef = useRef<HTMLDivElement>(null);
  const [canvasDimensions, setCanvasDimensions] = useState({ width: 300, height: 400 });

  const currentLsDeg = useMemo(() => getSolarLongitudeDeg(currentOrbitalAngleRad), [currentOrbitalAngleRad]);

  useEffect(() => {
    const container = containerRef.current;
    if (!container) return;

    const resizeObserver = new ResizeObserver(entries => {
      for (let entry of entries) {
        const { width, height } = entry.contentRect;
        if (width > 0 && height > 0) {
          setCanvasDimensions({ width, height });
        }
      }
    });

    resizeObserver.observe(container);
    const { width, height } = container.getBoundingClientRect();
    if (width > 0 && height > 0) {
      setCanvasDimensions({ width, height });
    }

    return () => resizeObserver.disconnect();
  }, []);

  useEffect(() => {
    const canvas = canvasRef.current;
    if (!canvas || canvasDimensions.width === 0 || canvasDimensions.height === 0) return;

    const dpr = window.devicePixelRatio || 1;
    canvas.width = canvasDimensions.width * dpr;
    canvas.height = canvasDimensions.height * dpr;
    canvas.style.width = `${canvasDimensions.width}px`;
    canvas.style.height = `${canvasDimensions.height}px`;

    const ctx = canvas.getContext('2d');
    if (!ctx) return;

    ctx.scale(dpr, dpr);

    const logicalWidth = canvasDimensions.width;
    const logicalHeight = canvasDimensions.height;

    ctx.clearRect(0, 0, logicalWidth, logicalHeight);
    ctx.fillStyle = 'rgba(0,0,0,0.35)';
    ctx.fillRect(0, 0, logicalWidth, logicalHeight);

    ctx.lineCap = 'round';
    ctx.lineJoin = 'round';

    const LsRad = currentLsDeg * Math.PI / 180;
    const delta = Math.asin(Math.sin(OBLIQ_DEG * Math.PI / 180) * Math.sin(LsRad));
    const daylightHours = calculateDaylightHours(45, delta * 180 / Math.PI);

    ctx.save();
    ctx.font = 'bold 18px sans-serif';
    ctx.textAlign = 'center';
    ctx.fillStyle = '#fff';
    ctx.fillText(`北纬45°白昼时长: ${daylightHours.toFixed(2)} 小时`, logicalWidth / 2, 25); // Position title within GLOBAL_TOP_OFFSET
    ctx.restore();

    // Actual area for chart drawing, below the title
    const chartAreaYStart = GLOBAL_TOP_OFFSET;
    const chartAreaHeight = logicalHeight - chartAreaYStart;

    // If chart area is too small, don't draw chart part
    if (chartAreaHeight < 2 * AXIS_MARGIN + TRACK_VISUAL_HEIGHT) {
        ctx.save();
        ctx.font = '12px sans-serif';
        ctx.textAlign = 'center';
        ctx.fillStyle = 'rgba(255,255,255,0.7)';
        ctx.fillText("Chart area too small to render", logicalWidth / 2, logicalHeight / 2);
        ctx.restore();
        return;
    }
    
    // Setup track geometry based on the actual chartAreaHeight
    // AXIS_MARGIN will be applied *within* this chartAreaHeight by the utility functions
    setupTrackGeometry(logicalWidth, chartAreaHeight, AXIS_MARGIN, TRACK_VISUAL_HEIGHT);

    ctx.save();
    // Translate context to the start of the chart drawing area
    ctx.translate(0, chartAreaYStart);

    // --- Draw Axes (within the translated chartArea) ---
    // Y coordinates are now relative to chartAreaYStart
    ctx.strokeStyle = '#ffffff';
    ctx.lineWidth = 2;
    ctx.beginPath();
    // Axis lines are from margin to chartAreaHeight - margin (relative to translated context)
    ctx.moveTo(AXIS_MARGIN, AXIS_MARGIN); // Top-left of drawable box
    ctx.lineTo(AXIS_MARGIN, chartAreaHeight - AXIS_MARGIN); // Bottom-left of drawable box
    ctx.lineTo(logicalWidth - AXIS_MARGIN, chartAreaHeight - AXIS_MARGIN); // Bottom-right of drawable box
    ctx.stroke();

    // Axis Labels
    ctx.fillStyle = '#fff';
    ctx.font = 'bold 12px sans-serif';
    ctx.textAlign = 'center';
    ctx.fillText('太阳赤纬 (°)', logicalWidth / 2, chartAreaHeight - AXIS_MARGIN + 20); // Below X-axis line

    ctx.textBaseline = 'top';
    ctx.textAlign = 'left';
    ctx.fillText('-23.44°', AXIS_MARGIN + 5, chartAreaHeight - AXIS_MARGIN + 2);
    ctx.textAlign = 'right';
    ctx.fillText('+23.44°', logicalWidth - AXIS_MARGIN - 5, chartAreaHeight - AXIS_MARGIN + 2);

    ctx.textAlign = 'right';
    ctx.textBaseline = 'middle';
    const yAxisLineTopY_rel = AXIS_MARGIN; // Relative to chartAreaYStart
    const yAxisLineBottomY_rel = chartAreaHeight - AXIS_MARGIN; // Relative to chartAreaYStart
    ctx.fillText('100%', AXIS_MARGIN - 8, yAxisLineTopY_rel + 5);
    ctx.fillText('0%', AXIS_MARGIN - 8, yAxisLineBottomY_rel);

    ctx.save();
    const yAxisTitleX = 15;
    const yAxisTitleY_rel = (yAxisLineTopY_rel + yAxisLineBottomY_rel) / 2;
    ctx.translate(yAxisTitleX, yAxisTitleY_rel);
    ctx.rotate(-Math.PI / 2);
    ctx.textAlign = 'center';
    ctx.font = 'bold 12px sans-serif';
    ctx.fillText('北半球阳照面积 (%)', 0, 0);
    ctx.restore();
    
    // TRACK_TRANSFORM_PARAMS.p1.y and p2.y are now relative to chartAreaHeight,
    // and already account for AXIS_MARGIN within that height.
    const p1y_rel = TRACK_TRANSFORM_PARAMS.p1.y;
    const p2y_rel = TRACK_TRANSFORM_PARAMS.p2.y;

    const yellowBarMinY_rel = Math.min(p1y_rel, p2y_rel);
    const yellowBarMaxY_rel = Math.max(p1y_rel, p2y_rel);

    ctx.save();
    ctx.globalAlpha = 0.45;
    ctx.fillStyle = '#ff0';
    ctx.fillRect(AXIS_MARGIN - 2, yellowBarMinY_rel, 4, yellowBarMaxY_rel - yellowBarMinY_rel);
    ctx.restore();

    ctx.save();
    ctx.strokeStyle = '#ff0';
    ctx.lineWidth = 1.5;
    ctx.setLineDash([5, 5]);
    ctx.beginPath();
    ctx.moveTo(AXIS_MARGIN, yellowBarMinY_rel);
    ctx.lineTo(logicalWidth - AXIS_MARGIN, yellowBarMinY_rel);
    ctx.moveTo(AXIS_MARGIN, yellowBarMaxY_rel);
    ctx.lineTo(logicalWidth - AXIS_MARGIN, yellowBarMaxY_rel);
    ctx.stroke();
    ctx.setLineDash([]);

    ctx.fillStyle = '#ff0';
    ctx.font = 'bold 12px sans-serif';
    ctx.textAlign = 'right';
    ctx.textBaseline = 'middle';
    ctx.fillText('37%', AXIS_MARGIN - 10, yellowBarMaxY_rel); // Max Y on canvas is min insolation
    ctx.fillText('63%', AXIS_MARGIN - 10, yellowBarMinY_rel); // Min Y on canvas is max insolation
    ctx.restore();

    ctx.save();
    // CX and CY are calculated by setupTrackGeometry relative to the chartArea (width, chartAreaHeight)
    // and AXIS_MARGIN within it. So CX, CY are already correct for this translated context.
    ctx.translate(TRACK_TRANSFORM_PARAMS.CX, TRACK_TRANSFORM_PARAMS.CY);
    ctx.rotate(TRACK_TRANSFORM_PARAMS.ALPHA);
    const xs = -TRACK_GEOMETRY_PARAMS.lenStraight / 2;
    const xe = TRACK_GEOMETRY_PARAMS.lenStraight / 2;
    const r_track = TRACK_GEOMETRY_PARAMS.r;
    ctx.strokeStyle = '#666';
    ctx.lineWidth = 1.5;
    ctx.beginPath();
    ctx.moveTo(xs, -r_track);
    ctx.lineTo(xe, -r_track);
    ctx.arc(xe, 0, r_track, -Math.PI / 2, Math.PI / 2, false);
    ctx.lineTo(xs, r_track);
    ctx.arc(xs, 0, r_track, Math.PI / 2, 3 * Math.PI / 2, false);
    ctx.stroke();
    ctx.restore(); // Undoes track rotation and translation

    ctx.save();
    // Translate and rotate for drawing the blue insolation line on the track
    ctx.translate(TRACK_TRANSFORM_PARAMS.CX, TRACK_TRANSFORM_PARAMS.CY);
    ctx.rotate(TRACK_TRANSFORM_PARAMS.ALPHA);
    ctx.beginPath();
    ctx.strokeStyle = '#44f';
    ctx.lineWidth = 2;
    const insolationFraction = (LsDeg_param: number) => {
        const obliqRad_calc = OBLIQ_DEG * Math.PI / 180;
        const delta_param = Math.asin(Math.sin(obliqRad_calc) * Math.sin(LsDeg_param * Math.PI / 180));
        return 50 + 50 * Math.sin(delta_param);
    };

    for (let i = 0; i <= 360; i++) {
      const Ls = i % 360;
      const ratio = mapSolarLongitudeToTrackRatioTestEarth2(Ls);
      const base = getTrackLocalPositionTestEarth2(ratio, TRACK_GEOMETRY_PARAMS);
      const py = base.y - ((insolationFraction(Ls) - 50) * 0.15); // Offset from track centerline
      if (i === 0) ctx.moveTo(base.x, py); else ctx.lineTo(base.x, py);
    }
    ctx.stroke();
    ctx.restore(); // Undoes track rotation and translation for blue line

    // Solar Term Labels (need to be drawn in the main translated context, not track's rotated one)
    ctx.font = '10px sans-serif';
    ctx.fillStyle = '#fff';
    ctx.textBaseline = 'middle';
    const upperTerms: { name: string; x: number; y: number; Ls: number }[] = [];
    const lowerTerms: { name: string; x: number; y: number; Ls: number }[] = [];

    TERM_NAMES.forEach((name, i) => {
      const termLs = (270 + i * 15) % 360;
      const ratio = mapSolarLongitudeToTrackRatioTestEarth2(termLs);
      const localPos = getTrackLocalPositionTestEarth2(ratio, TRACK_GEOMETRY_PARAMS);
      // Transform local track pos to current context (already translated by chartAreaYStart, CX, CY, and rotated by ALPHA)
      const canvasPos = transformTrackLocalToCanvasTestEarth2(localPos.x, localPos.y, TRACK_TRANSFORM_PARAMS);
      // canvasPos.x and canvasPos.y are now relative to the chartArea's top-left (0, chartAreaYStart in absolute)

      const isUp = localPos.y < 0;
      (isUp ? upperTerms : lowerTerms).push({ name, x: canvasPos.x, y: canvasPos.y, Ls: termLs });
    });

    upperTerms.sort((a, b) => a.x - b.x);
    lowerTerms.sort((a, b) => a.x - b.x);

    const plotTermLabel = (o: { name: string; x: number; y: number; Ls: number }, ly_abs_for_text: number, isUp: boolean) => {
      ctx.strokeStyle = '#777';
      ctx.lineWidth = 1;
      ctx.beginPath();
      ctx.moveTo(o.x, o.y);
      ctx.lineTo(o.x, o.y + (isUp ? -5 : 5));
      ctx.lineTo(o.x, ly_abs_for_text);
      ctx.stroke();
      ctx.textAlign = 'center';
      ctx.fillStyle = '#fff';
      ctx.fillText(`${o.name}(${TERM_TO_MONTH[o.name as keyof typeof TERM_TO_MONTH]}月)`, o.x, ly_abs_for_text);
    };

    upperTerms.forEach((o, idx) => {
        const labelTargetY = o.y - (STAGGER_BASE_OFFSET + (idx % NUM_STAGGER_LEVELS) * STAGGER_LEVEL_HEIGHT);
        plotTermLabel(o, labelTargetY, true);
    });

    lowerTerms.forEach((o, idx) => {
        const labelTargetY = o.y + (STAGGER_BASE_OFFSET + (idx % NUM_STAGGER_LEVELS) * STAGGER_LEVEL_HEIGHT);
        plotTermLabel(o, labelTargetY, false);
    });

    const currentTermRatio = mapSolarLongitudeToTrackRatioTestEarth2(currentLsDeg);
    const currentLocalPos = getTrackLocalPositionTestEarth2(currentTermRatio, TRACK_GEOMETRY_PARAMS);
    const currentCanvasPos = transformTrackLocalToCanvasTestEarth2(currentLocalPos.x, currentLocalPos.y, TRACK_TRANSFORM_PARAMS);
    // currentCanvasPos is relative to chartArea top-left.

    ctx.fillStyle = '#ff0';
    ctx.beginPath();
    ctx.arc(currentCanvasPos.x, currentCanvasPos.y, 4, 0, Math.PI * 2);
    ctx.fill();

    ctx.font = 'bold 12px sans-serif';
    ctx.fillStyle = '#fff';
    ctx.textAlign = 'left';
    ctx.textBaseline = 'bottom';
    const currentTermIndex = getSolarTermIndexFromLs(currentLsDeg);
    const currentTermName = TERM_NAMES[currentTermIndex];
    ctx.fillText(
      `${currentTermName}(${TERM_TO_MONTH[currentTermName as keyof typeof TERM_TO_MONTH]}月)`,
      currentCanvasPos.x + 8,
      currentCanvasPos.y - 6
    );

    ctx.restore(); // Undoes the main ctx.translate(0, chartAreaYStart)

  }, [currentLsDeg, canvasDimensions]);

  return (
    <div ref={containerRef} className="w-full h-full overflow-hidden">
        <canvas
            ref={canvasRef}
            aria-label="Northern Hemisphere Insolation and Solar Terms Chart"
            className="rounded"
        />
    </div>
  );
};

export default InsolationChart;
